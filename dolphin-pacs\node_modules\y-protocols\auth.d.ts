export const messagePermissionDenied: 0;
export function writePermissionDenied(encoder: encoding.Encoder, reason: string): void;
export function readAuthMessage(decoder: decoding.Decoder, y: Y.Doc, permissionDeniedHandler: PermissionDeniedHandler): void;
export type PermissionDeniedHandler = (y: any, reason: string) => any;
import * as encoding from 'lib0/encoding';
import * as decoding from 'lib0/decoding';
import * as Y from 'yjs';
//# sourceMappingURL=auth.d.ts.map