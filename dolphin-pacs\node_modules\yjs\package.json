{"name": "yjs", "version": "13.6.27", "description": "Shared Editing Library", "main": "./dist/yjs.cjs", "module": "./dist/yjs.mjs", "types": "./dist/src/index.d.ts", "type": "module", "sideEffects": false, "funding": {"type": "GitHub Sponsors ❤", "url": "https://github.com/sponsors/dmonad"}, "scripts": {"clean": "rm -rf dist docs", "test": "npm run dist && NODE_ENV=development node ./dist/tests.cjs --repetition-time 50", "test-extensive": "npm run lint && npm run dist && node ./dist/tests.cjs --production --repetition-time 10000", "dist": "npm run clean && rollup -c && tsc", "watch": "rollup -wc", "lint": "markdownlint README.md && standard && tsc", "docs": "rm -rf docs; jsdoc --configure ./.jsdoc.json --verbose --readme ./README.md --package ./package.json || true", "serve-docs": "npm run docs && http-server ./docs/", "preversion": "npm run lint && PRODUCTION=1 npm run dist && npm run docs && node ./dist/tests.cjs --repetition-time 1000 && test -e dist/src/index.d.ts && test -e dist/yjs.cjs && test -e dist/yjs.cjs", "debug": "concurrently 'http-server -o test.html' 'npm run watch'", "trace-deopt": "clear && rollup -c  && node --trace-deopt dist/test.cjs", "trace-opt": "clear && rollup -c  && node --trace-opt dist/test.cjs"}, "exports": {".": {"types": "./dist/src/index.d.ts", "module": "./dist/yjs.mjs", "import": "./dist/yjs.mjs", "require": "./dist/yjs.cjs"}, "./src/index.js": "./src/index.js", "./tests/testHelper.js": "./tests/testHelper.js", "./testHelper": "./dist/testHelper.mjs", "./package.json": "./package.json"}, "files": ["dist/yjs.*", "dist/src", "src", "tests/testHelper.js", "dist/testHelper.mjs", "sponsor-y.js"], "dictionaries": {"test": "tests"}, "standard": {"ignore": ["/dist", "/node_modules", "/docs"]}, "repository": {"type": "git", "url": "https://github.com/yjs/yjs.git"}, "keywords": ["<PERSON><PERSON><PERSON>", "CRDT", "offline", "offline-first", "shared-editing", "concurrency", "collaboration"], "author": "<PERSON>", "email": "<EMAIL>", "license": "MIT", "bugs": {"url": "https://github.com/yjs/yjs/issues"}, "homepage": "https://docs.yjs.dev", "dependencies": {"lib0": "^0.2.99"}, "devDependencies": {"@rollup/plugin-commonjs": "^24.0.1", "@rollup/plugin-node-resolve": "^15.0.1", "@types/node": "^18.15.5", "concurrently": "^3.6.1", "http-server": "^0.12.3", "jsdoc": "^3.6.7", "markdownlint-cli": "^0.41.0", "rollup": "^3.20.0", "standard": "^16.0.4", "tui-jsdoc-template": "^1.2.2", "typescript": "^4.9.5", "y-protocols": "^1.0.5"}, "engines": {"npm": ">=8.0.0", "node": ">=16.0.0"}}