{"version": 3, "file": "auth.cjs", "sources": ["../auth.js"], "sourcesContent": ["\nimport * as Y from 'yjs' // eslint-disable-line\nimport * as encoding from 'lib0/encoding'\nimport * as decoding from 'lib0/decoding'\n\nexport const messagePermissionDenied = 0\n\n/**\n * @param {encoding.Encoder} encoder\n * @param {string} reason\n */\nexport const writePermissionDenied = (encoder, reason) => {\n  encoding.writeVarUint(encoder, messagePermissionDenied)\n  encoding.writeVarString(encoder, reason)\n}\n\n/**\n * @callback PermissionDeniedHandler\n * @param {any} y\n * @param {string} reason\n */\n\n/**\n *\n * @param {decoding.Decoder} decoder\n * @param {Y.Doc} y\n * @param {PermissionDeniedHandler} permissionDeniedHandler\n */\nexport const readAuthMessage = (decoder, y, permissionDeniedHandler) => {\n  switch (decoding.readVarUint(decoder)) {\n    case messagePermissionDenied: permissionDeniedHandler(y, decoding.readVarString(decoder))\n  }\n}\n"], "names": ["encoding", "decoding"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAKY,MAAC,uBAAuB,GAAG,EAAC;AACxC;AACA;AACA;AACA;AACA;AACY,MAAC,qBAAqB,GAAG,CAAC,OAAO,EAAE,MAAM,KAAK;AAC1D,EAAEA,mBAAQ,CAAC,YAAY,CAAC,OAAO,EAAE,uBAAuB,EAAC;AACzD,EAAEA,mBAAQ,CAAC,cAAc,CAAC,OAAO,EAAE,MAAM,EAAC;AAC1C,EAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACY,MAAC,eAAe,GAAG,CAAC,OAAO,EAAE,CAAC,EAAE,uBAAuB,KAAK;AACxE,EAAE,QAAQC,mBAAQ,CAAC,WAAW,CAAC,OAAO,CAAC;AACvC,IAAI,KAAK,uBAAuB,EAAE,uBAAuB,CAAC,CAAC,EAAEA,mBAAQ,CAAC,aAAa,CAAC,OAAO,CAAC,EAAC;AAC7F,GAAG;AACH;;;;;;"}